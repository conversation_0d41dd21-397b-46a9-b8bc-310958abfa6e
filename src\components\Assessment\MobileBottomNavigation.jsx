import { ChevronLeft, ChevronRight, Check } from 'lucide-react';

const MobileBottomNavigation = ({
  currentPage,
  totalPages,
  currentStep,
  totalSteps,
  isLastAssessment,
  isAssessmentComplete,
  isProcessingSubmit,
  isAutoFillMode,
  onPreviousCategory,
  onNextCategory,
  onPrevious,
  onSubmitWithValidation,
  onManualSubmit,
  currentCategoryData
}) => {
  return (
    <div className="lg:hidden sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-30">
      <div className="px-3 sm:px-4 py-3 sm:py-4">
        {/* Header - Category info */}
        <div className="text-center mb-3">
          <div className="text-sm font-semibold text-gray-900">
            {currentCategoryData?.name}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            Category {currentPage + 1} of {totalPages}
          </div>
        </div>

        {/* Navigation Layout: Previous | Position | Next */}
        <div className="grid grid-cols-3 items-center gap-2 max-w-sm mx-auto">
          {/* Previous Button */}
          <div className="flex justify-start">
            {currentPage === 0 && currentStep > 1 ? (
              <button
                onClick={onPrevious}
                className="flex items-center space-x-1 px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="text-xs font-medium hidden sm:inline">Prev</span>
              </button>
            ) : currentPage > 0 ? (
              <button
                onClick={onPreviousCategory}
                className="flex items-center space-x-1 px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="text-xs font-medium hidden sm:inline">Prev</span>
              </button>
            ) : (
              <div></div>
            )}
          </div>

          {/* Center Position Indicator */}
          <div className="flex justify-center">
            <div className="flex space-x-1">
              {Array.from({ length: totalPages }, (_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentPage
                      ? 'bg-gray-900'
                      : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Next/Submit Button */}
          <div className="flex justify-end">
            {/* Submit Assessment Button */}
            {currentPage === totalPages - 1 && isLastAssessment && !isAutoFillMode ? (
              <button
                onClick={onSubmitWithValidation}
                disabled={!isAssessmentComplete() || isProcessingSubmit}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                <Check className="h-4 w-4" />
                <span className="text-xs font-medium">
                  {isProcessingSubmit ? 'Submit...' : 'Submit'}
                </span>
              </button>
            ) : /* Manual Submit Button for Auto-Fill Mode */
            isAutoFillMode && isLastAssessment ? (
              <button
                onClick={onManualSubmit}
                disabled={isProcessingSubmit}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                <Check className="h-4 w-4" />
                <span className="text-xs font-medium">
                  {isProcessingSubmit ? 'Submit...' : 'Submit All'}
                </span>
              </button>
            ) : /* Next Assessment Button */
            currentPage === totalPages - 1 && !isLastAssessment ? (
              <button
                onClick={onSubmitWithValidation}
                disabled={!isAssessmentComplete() || isProcessingSubmit}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                <span className="text-xs font-medium">
                  {isProcessingSubmit ? 'Next...' : 'Next'}
                </span>
                <ChevronRight className="h-4 w-4" />
              </button>
            ) : /* Next Category Button */
            currentPage < totalPages - 1 ? (
              <button
                onClick={onNextCategory}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-all"
              >
                <span className="text-xs font-medium hidden sm:inline">Next</span>
                <ChevronRight className="h-4 w-4" />
              </button>
            ) : (
              <div></div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileBottomNavigation;